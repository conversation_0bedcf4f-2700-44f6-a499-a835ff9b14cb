import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { MyLogger } from './MyLogger';
import * as mysql from 'mysql2'

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useLogger(new MyLogger());

  // 测试MySQL连接
  try {
    const connection = mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: 'xuanidea1999!',
      database: 'practice',
    });

    connection.query('SELECT 1 + 1 AS solution', function (err, results, fields) {
      if (err) {
        console.error('MySQL连接错误:', err);
      } else {
        console.log('MySQL连接成功:', results);
      }
      connection.end();
    });
  } catch (error) {
    console.error('MySQL连接失败:', error);
  }

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
